/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "12", height: "20", x: "6", y: "2", rx: "2", key: "1oxtiu" }]
];
const RectangleVertical = createLucideIcon("rectangle-vertical", __iconNode);

export { __iconNode, RectangleVertical as default };
//# sourceMappingURL=rectangle-vertical.js.map
